# GoTask 教学课程

欢迎来到GoTask框架的教学课程！这个目录包含了10个渐进式的课程，从基础到高级，帮助你全面掌握GoTask框架的使用。

## 课程结构

### 基础课程 (Lesson 1-3)
- **Lesson 1**: 基础Task使用 - 学习最基本的任务定义和执行
- **Lesson 2**: Job容器管理 - 学习如何管理多个子任务
- **Lesson 3**: Work长期运行任务 - 学习异步任务的执行

### 中级课程 (Lesson 4-6)
- **Lesson 4**: ChannelTask通道任务 - 学习任务间通信
- **Lesson 5**: TickTask定时任务 - 学习定时器任务
- **Lesson 6**: RootManager根任务管理 - 学习应用程序级管理

### 高级课程 (Lesson 7-9)
- **Lesson 7**: 资源管理与清理 - 学习资源生命周期管理
- **Lesson 8**: 重试机制 - 学习错误恢复策略
- **Lesson 9**: 事件监听与回调 - 学习任务间协作

### 综合应用 (Lesson 10)
- **Lesson 10**: 综合应用案例 - 完整应用程序示例

## 如何使用这些课程

### 1. 按顺序学习
建议按照课程编号顺序学习，每个课程都建立在前一个课程的基础上。

### 2. 动手实践
每个课程都包含TODO注释，你需要：
1. 阅读课程说明
2. 按照TODO注释的提示取消注释
3. 运行程序验证结果
4. 理解每个概念的作用

### 3. 运行课程
```bash
# 进入课程目录
cd lessons/lesson01

# 运行课程
go run main.go
```

### 4. 验证学习效果
每个课程都有预期的输出结果，确保你的程序输出与预期一致。

## 课程特色

### 渐进式设计
- 从简单到复杂，循序渐进
- 每个概念都有实际应用场景
- 避免一次性学习过多概念

### 实践导向
- 每个课程都是可运行的完整程序
- 通过TODO注释引导学习
- 提供详细的预期输出

### 全面覆盖
- 涵盖GoTask框架的所有核心功能
- 包含最佳实践和常见模式
- 提供完整的应用程序示例

## 学习建议

### 1. 理解概念
不要只是取消注释，要理解每个方法的作用和适用场景。

### 2. 实验修改
尝试修改参数，观察不同的行为，加深理解。

### 3. 查看源码
结合GoTask框架的源码，理解内部实现机制。

### 4. 构建项目
学完所有课程后，尝试构建自己的项目。

## 故障排除

### 常见问题

1. **编译错误**
   - 确保Go版本 >= 1.23
   - 检查go.mod文件中的依赖

2. **运行时错误**
   - 确保按照TODO注释正确取消注释
   - 检查任务的生命周期管理

3. **输出不符合预期**
   - 检查是否所有TODO都已取消注释
   - 确认任务启动和停止的顺序

### 获取帮助
- 查看GoTask项目文档
- 阅读源码注释
- 参考测试用例

## 下一步

完成所有课程后，你可以：
1. 阅读GoTask框架的完整文档
2. 查看dashboard示例项目
3. 构建自己的GoTask应用程序
4. 参与GoTask社区讨论

祝你学习愉快！🚀
