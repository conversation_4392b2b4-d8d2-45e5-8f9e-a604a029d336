
#root {
  width: 100%;
  height: 100vh;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  max-width: none;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', '<PERSON><PERSON><PERSON>', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

.ant-layout {
  background: #f0f2f5;
}

.ant-layout-header {
  height: 64px;
  line-height: 64px;
}

.ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border-radius: 8px;
}

.ant-tree-node-content-wrapper {
  border-radius: 4px;
}

.ant-tree-node-content-wrapper:hover {
  background-color: #f5f5f5;
}

.ant-descriptions-item-label {
  font-weight: 600;
}

.ant-tag {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

.ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

.ant-pagination {
  margin-top: 16px;
}

/* 强制任务树占满全宽 */
.ant-layout,
.ant-layout-content {
  width: 100% !important;
  max-width: none !important;
}

.ant-card {
  width: 100% !important;
  max-width: none !important;
}