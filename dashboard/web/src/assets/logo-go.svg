<svg width="100" height="40" viewBox="0 0 100 40" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Go语言风格的G字母 -->
  <g transform="translate(5, 5)">
    <path d="M8 2 L20 2 L20 8 L14 8 L14 18 L8 18 Z" fill="#00ADD8" stroke="#00ADD8" stroke-width="1" />
    <path d="M14 8 L20 8 L20 14 L14 14 Z" fill="white" />
  </g>

  <!-- 任务管理图标 -->
  <g transform="translate(35, 8)">
    <!-- 任务队列 -->
    <rect x="0" y="0" width="12" height="8" fill="#1890ff" rx="1" />
    <rect x="0" y="10" width="12" height="8" fill="#52c41a" rx="1" />
    <rect x="0" y="20" width="12" height="8" fill="#faad14" rx="1" />

    <!-- 箭头 -->
    <path d="M16 12 L22 12 L20 10 M22 12 L20 14" stroke="#1890ff" stroke-width="2" fill="none" stroke-linecap="round" />
  </g>

  <!-- 文字 -->
  <text x="60" y="16" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#1890ff">GoTask</text>
  <text x="60" y="26" font-family="Arial, sans-serif" font-size="7" fill="#666">Task System</text>
</svg>