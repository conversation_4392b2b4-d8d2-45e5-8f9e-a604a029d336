{"app": {"title": "GoTask Task Management System"}, "task": {"tree": "Task Tree", "detail": "Task Detail", "createDemo": "Create Demo Task", "refresh": "Refresh", "restart": "<PERSON><PERSON>", "stop": "Stop", "stopTask": "Stop Task", "noData": "No task data available", "selectTask": "Please select a task to view details", "total": "Total {{count}} tasks"}, "table": {"owner": "Owner", "taskId": "Task ID", "type": "Type", "state": "State", "startTime": "Start Time", "endTime": "End Time", "duration": "Duration", "retryCount": "Retry Count", "startReason": "Start Reason", "stopReason": "Stop Reason", "actions": "Actions"}, "taskType": {"task": "Task", "job": "Job", "work": "Work", "channel": "Channel"}, "taskState": {"init": "Initializing", "starting": "Starting", "started": "Started", "running": "Running", "going": "Going", "disposing": "Disposing", "disposed": "Disposed"}, "taskDetail": {"taskId": "Task ID", "taskType": "Task Type", "ownerType": "Owner Type", "taskState": "Task State", "taskLevel": "Task Level", "retryCount": "Retry Count", "startTime": "Start Time", "duration": "Duration", "startReason": "Start Reason", "stopReason": "Stop Reason", "parentTaskId": "Parent Task ID", "childTaskCount": "Child Task Count", "description": "Description"}, "message": {"getTaskTreeFailed": "Failed to get task tree", "createDemoTaskSuccess": "Demo task created successfully", "createDemoTaskFailed": "Failed to create demo task", "taskRestarted": "Task restarted", "restartTaskFailed": "Failed to restart task", "taskStopped": "Task stopped", "stopTaskFailed": "Failed to stop task"}, "language": {"switch": "Switch Language", "chinese": "中文", "english": "English"}, "history": {"title": "Task History", "refresh": "Refresh", "view": "View", "startDate": "Start Date", "endDate": "End Date", "total": "Total {{count}} records", "session": "Session", "sessionList": "Session List", "taskList": "Task List", "tableView": "Table View", "ganttView": "Timeline", "filter": "Filter", "ownerType": "Owner Type", "taskType": "Task Type", "sessionId": "Session ID", "parentId": "Parent Task ID", "timeRange": "Time Range", "reset": "Reset", "apply": "Apply", "statistics": "Statistics", "totalTasks": "Total Tasks", "totalDuration": "Total Duration", "averageDuration": "Average Duration", "ownerTypeStats": "By Owner", "taskTypeStats": "By Type", "stateStats": "By State"}, "session": {"title": "Session Information", "id": "Session ID", "startTime": "Start Time", "endTime": "End Time", "pid": "Process ID", "args": "Start Arguments"}, "timeUnits": {"day": "d", "hour": "h", "minute": "m", "second": "s"}}