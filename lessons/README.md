# GoTask Learning Course

Welcome to the GoTask framework learning course! This directory contains 10 progressive lessons, from basic to advanced, to help you master the GoTask framework comprehensively.

## Course Structure

### Basic Courses (Lesson 1-3)
- **Lesson 1**: Basic Task Usage - Learn the most fundamental task definition and execution
- **Lesson 2**: Job Container Management - Learn how to manage multiple subtasks
- **Lesson 3**: Work Long-running Tasks - Learn asynchronous task execution

### Intermediate Courses (Lesson 4-6)
- **Lesson 4**: ChannelTask Communication - Learn inter-task communication
- **Lesson 5**: TickTask Scheduled Tasks - Learn timer-based tasks
- **Lesson 6**: RootManager Root Task Management - Learn application-level management

### Advanced Courses (Lesson 7-9)
- **Lesson 7**: Resource Management and Cleanup - Learn resource lifecycle management
- **Lesson 8**: Retry Mechanism - Learn error recovery strategies
- **Lesson 9**: Event Listening and Callbacks - Learn task collaboration

### Comprehensive Application (Lesson 10)
- **Lesson 10**: Comprehensive Application Example - Complete application demonstration

## How to Use These Courses

### 1. Learn in Order
It's recommended to learn in numerical order, as each lesson builds upon the previous one.

### 2. Hands-on Practice
Each lesson contains TODO comments. You need to:
1. Read the lesson instructions
2. Uncomment according to TODO comment hints
3. Run the program to verify results
4. Understand the purpose of each concept

### 3. Running Lessons
```bash
# Enter the lesson directory
cd lessons/lesson01

# Run the lesson
go run main.go
```

### 4. Verify Learning Outcomes
Each lesson has expected output results. Ensure your program output matches expectations.

## Course Features

### Progressive Design
- From simple to complex, step by step
- Each concept has practical application scenarios
- Avoid learning too many concepts at once

### Practice-Oriented
- Each lesson is a runnable complete program
- Guided learning through TODO comments
- Provides detailed expected output

### Comprehensive Coverage
- Covers all core features of GoTask framework
- Includes best practices and common patterns
- Provides complete application examples

## Learning Suggestions

### 1. Understand Concepts
Don't just uncomment - understand the purpose and applicable scenarios of each method.

### 2. Experiment with Modifications
Try modifying parameters, observe different behaviors, and deepen understanding.

### 3. Review Source Code
Combine with GoTask framework source code to understand internal implementation mechanisms.

### 4. Build Projects
After completing all lessons, try building your own projects.

## Troubleshooting

### Common Issues

1. **Compilation Errors**
   - Ensure Go version >= 1.23
   - Check dependencies in go.mod file

2. **Runtime Errors**
   - Ensure correct uncommenting according to TODO comments
   - Check task lifecycle management

3. **Unexpected Output**
   - Check if all TODOs have been uncommented
   - Confirm task start and stop order

### Getting Help
- Review GoTask project documentation
- Read source code comments
- Refer to test cases

## Next Steps

After completing all lessons, you can:
1. Read complete GoTask framework documentation
2. Check dashboard example project
3. Build your own GoTask applications
4. Participate in GoTask community discussions

Happy learning! 🚀